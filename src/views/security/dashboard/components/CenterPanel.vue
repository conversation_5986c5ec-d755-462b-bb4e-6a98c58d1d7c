<template>
  <div class="center-panel">
    <!-- 中国3D地图 -->
    <div class="map-container">
      <div ref="mapChartRef" class="map-chart"></div>
    </div>

    <!-- 关键指标概览 -->
    <div class="metrics-container">
      <div class="metrics-grid">
        <div
          v-for="(metric, index) in metricsData"
          :key="index"
          class="metric-card"
          :style="{ background: metric.background }"
        >
          <div class="metric-content">
            <div class="metric-header">
              <span class="metric-title">{{ metric.title }}</span>
              <Icon :icon="metric.icon" :size="24" class="metric-icon" />
            </div>
            <div class="metric-value">{{ metric.value }}</div>
            <div class="metric-change" :style="{ color: metric.changeColor }">
              <Icon :icon="metric.changeIcon" :size="14" />
              <span>{{ metric.change }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, onMounted } from 'vue';
  import Icon from '@/components/Icon/Icon.vue';
  import { useECharts } from '@/hooks/web/useECharts';
  import echarts from '@/utils/lib/echarts';

  const mapChartRef = ref();
  const { setOptions: setMapOptions, getInstance } = useECharts(mapChartRef);

  // 关键指标数据
  const metricsData = ref([
    {
      title: '今日活跃次数',
      value: '12',
      change: '+8%',
      changeColor: '#E67E22',
      changeIcon: 'ant-design:arrow-up-outlined',
      background: 'linear-gradient(135deg, #E74C3C 0%, #C0392B 100%)',
      icon: 'ant-design:fire-outlined',
    },
    {
      title: '今日作业任务',
      value: '47',
      change: '+8%',
      changeColor: '#3498DB',
      changeIcon: 'ant-design:arrow-up-outlined',
      background: 'linear-gradient(135deg, #26B99A 0%, #1ABC9C 100%)',
      icon: 'ant-design:tool-outlined',
    },
    {
      title: 'AR眼镜使用次数',
      value: '32',
      change: '+5%',
      changeColor: '#5DADE2',
      changeIcon: 'ant-design:arrow-up-outlined',
      background: 'linear-gradient(135deg, #3498DB 0%, #2980B9 100%)',
      icon: 'ant-design:eye-outlined',
    },
    {
      title: '工程车辆检修',
      value: '8',
      change: '30%',
      changeColor: '#95A5A6',
      changeIcon: 'ant-design:percentage-outlined',
      background: 'linear-gradient(135deg, #8E44AD 0%, #7D3C98 100%)',
      icon: 'ant-design:car-outlined',
    },
  ]);

  // 城市数据 - 基于 map.html 中的配置
  const cityData = [
    {
      name: '成都',
      coord: [104.066, 30.572],
      value: 100,
      status: 'online',
      info: '成都数据中心<br/>状态：正常运行<br/>节点数：156',
    },
    {
      name: '石家庄',
      coord: [114.514, 38.042],
      value: 80,
      status: 'online',
      info: '石家庄数据中心<br/>状态：正常运行<br/>节点数：89',
    },
    {
      name: '上海',
      coord: [121.473, 31.23],
      value: 60,
      status: 'building',
      info: '上海数据中心<br/>状态：建设中<br/>预计完成：2024年Q2',
    },
    {
      name: '北京',
      coord: [116.407, 39.904],
      value: 120,
      status: 'online',
      info: '北京数据中心<br/>状态：正常运行<br/>节点数：203',
    },
    {
      name: '深圳',
      coord: [114.057, 22.543],
      value: 90,
      status: 'online',
      info: '深圳数据中心<br/>状态：正常运行<br/>节点数：134',
    },
    {
      name: '西安',
      coord: [108.939, 34.341],
      value: 40,
      status: 'building',
      info: '西安数据中心<br/>状态：建设中<br/>预计完成：2024年Q3',
    },
  ];

  // 初始化3D地图
  function initMap() {
    // 使用3D地图配置 - 优化后的高级设计风格
    const map3DOption = {
      backgroundColor: 'transparent',
      globe: {
        show: false,
      },
      geo3D: {
        map: 'china',
        roam: true,
        itemStyle: {
          // 使用与页面主题一致的深蓝渐变
          color: function (_params) {
            return new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#40E0FF' }, // 顶部：页面强调色（青蓝）
              { offset: 0.3, color: '#2E86C1' }, // 中部：中蓝
              { offset: 0.7, color: '#1B4F72' }, // 下部：深蓝
              { offset: 1, color: '#0B243B' }, // 底部：与卡片背景呼应的深蓝
            ]);
          },
          opacity: 0.9,
          borderWidth: 1.5,
          borderColor: '#40E0FF', // 使用页面强调色作为边框
        },
        emphasis: {
          itemStyle: {
            color: function (_params) {
              return new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#5DADE2' }, // 悬停时更亮的青蓝色
                { offset: 0.5, color: '#3498DB' },
                { offset: 1, color: '#2E86C1' },
              ]);
            },
            borderColor: '#80FF80', // 悬停时的亮绿边框
            borderWidth: 2.5,
            opacity: 1,
          },
        },
        light: {
          main: {
            color: '#ffffff',
            intensity: 1.8, // 增强主光源强度
            shadow: true,
            shadowQuality: 'high',
            alpha: 25, // 调整光照角度
            beta: 50,
          },
          ambient: {
            color: '#40E0FF', // 环境光使用页面强调色
            intensity: 0.6, // 增强环境光
          },
        },
        viewControl: {
          autoRotate: false,
          distance: 65, // 稍微拉远视距
          alpha: 65,
          beta: 15,
          center: [5, 20, 0],
          animation: true,
          animationDurationUpdate: 1200,
          damping: 0.85,
          rotateSensitivity: 1,
          zoomSensitivity: 1,
          panSensitivity: 1,
        },
        regionHeight: 10, // 增加地图高度，提升立体感
        postEffect: {
          enable: true,
          bloom: {
            enable: true,
            intensity: 0.5, // 增强发光效果
            bloomRadius: 0.8,
          },
          SSAO: {
            enable: true,
            radius: 3,
            intensity: 1.2,
          },
          // 添加色调映射，提升视觉质量
          colorCorrection: {
            enable: true,
            exposure: 1.2,
            brightness: 0.1,
            contrast: 1.1,
            saturation: 1.2,
          },
        },
      },
      series: [
        // 3D地图主体
        {
          type: 'map3D',
          map: 'china',
          regionHeight: 10, // 与 geo3D 保持一致
          itemStyle: {
            color: function (_params) {
              return new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#40E0FF' }, // 顶部：页面强调色
                { offset: 0.3, color: '#2E86C1' }, // 中部：中蓝
                { offset: 0.7, color: '#1B4F72' }, // 下部：深蓝
                { offset: 1, color: '#0B243B' }, // 底部：深蓝
              ]);
            },
            opacity: 0.9,
            borderWidth: 1.5,
            borderColor: '#40E0FF',
            borderType: 'solid',
          },
          emphasis: {
            itemStyle: {
              color: function (_params) {
                return new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: '#5DADE2' },
                  { offset: 0.5, color: '#3498DB' },
                  { offset: 1, color: '#2E86C1' },
                ]);
              },
              borderColor: '#80FF80',
              borderWidth: 2.5,
              opacity: 1,
            },
          },
          light: {
            main: {
              color: '#ffffff',
              intensity: 1.8,
              shadow: true,
              shadowQuality: 'high',
              alpha: 25,
              beta: 50,
            },
            ambient: {
              color: '#40E0FF',
              intensity: 0.6,
            },
          },
        },
        // 城市标注点 - 优化后的高级样式
        {
          type: 'scatter3D',
          coordinateSystem: 'geo3D',
          data: cityData.map((city) => ({
            name: city.name,
            value: [...city.coord, 18], // 提升标注点高度
            itemStyle: {
              // 使用渐变色和发光效果
              color: function () {
                if (city.status === 'online') {
                  return new echarts.graphic.RadialGradient(0.5, 0.5, 0.8, [
                    { offset: 0, color: '#FFFFFF' }, // 中心白色
                    { offset: 0.3, color: '#40E0FF' }, // 页面强调色
                    { offset: 0.7, color: '#2E86C1' }, // 中蓝
                    { offset: 1, color: '#1B4F72' }, // 外围深蓝
                  ]);
                } else {
                  return new echarts.graphic.RadialGradient(0.5, 0.5, 0.8, [
                    { offset: 0, color: '#FFFFFF' }, // 中心白色
                    { offset: 0.3, color: '#E67E22' }, // 橙色（与页面指标卡片呼应）
                    { offset: 0.7, color: '#D35400' }, // 深橙
                    { offset: 1, color: '#A04000' }, // 外围深橙
                  ]);
                }
              },
              opacity: 0.95,
            },
            status: city.status,
            info: city.info,
          })),
          symbol: 'circle',
          symbolSize: function (_, params) {
            // 根据状态调整大小，在线状态更大更醒目
            return params.data.status === 'online' ? 18 : 15;
          },
          itemStyle: {
            borderWidth: 2.5,
            borderColor: function (params) {
              return params.data.status === 'online' ? '#40E0FF' : '#E67E22';
            },
            // 添加发光效果
            shadowBlur: 15,
            shadowColor: function (params) {
              return params.data.status === 'online' ? '#40E0FF' : '#E67E22';
            },
          },
          label: {
            show: true,
            position: 'top',
            distance: 8,
            color: '#FFFFFF',
            fontSize: 13,
            fontWeight: 'bold',
            fontFamily: 'Microsoft YaHei, Arial, sans-serif',
            // 使用玻璃态样式，与页面整体风格一致
            backgroundColor: 'rgba(11, 36, 59, 0.95)', // 与卡片背景一致
            padding: [6, 12],
            borderRadius: 6,
            borderWidth: 1,
            borderColor: function (params) {
              return params.data.status === 'online' ? '#40E0FF' : '#E67E22';
            },
            // 添加文字阴影和背景模糊效果
            textShadowColor: 'rgba(0, 0, 0, 0.8)',
            textShadowBlur: 2,
            textShadowOffsetX: 1,
            textShadowOffsetY: 1,
          },
          emphasis: {
            itemStyle: {
              opacity: 1,
              borderWidth: 4,
              shadowBlur: 25, // 悬停时增强发光
            },
            label: {
              show: true,
              fontSize: 15,
              fontWeight: 'bold',
              backgroundColor: 'rgba(11, 36, 59, 1)', // 悬停时背景更不透明
            },
          },
        },
      ],
      animation: true,
      animationDuration: 2000,
      animationEasing: 'cubicOut',
    };

    try {
      setMapOptions(map3DOption);
    } catch (error) {
      // 如果3D失败，显示错误信息
      console.error('3D地图初始化失败:', error);
    }

    // 添加事件监听
    setTimeout(() => {
      const chartInstance = getInstance();
      if (chartInstance) {
        chartInstance.on('click', handleMapClick);
        chartInstance.on('mouseover', handleMapMouseOver);
        chartInstance.on('mouseout', handleMapMouseOut);
      }
    }, 200);

    // 延迟启动入场动画，确保地图已经渲染完成
    setTimeout(() => {
      startAnimation();
    }, 300);
  }

  function startAnimation() {
    const chartInstance = getInstance();
    if (!chartInstance) return;

    // 初始隐藏地图
    chartInstance.setOption({
      geo3D: {
        regionHeight: 0,
        itemStyle: {
          opacity: 0,
        },
      },
    });

    // 第一阶段：地图主体升起动画
    setTimeout(() => {
      chartInstance.setOption({
        geo3D: {
          regionHeight: 10, // 与最终高度一致
          itemStyle: {
            opacity: 0.9,
          },
        },
        series: [
          {
            regionHeight: 10,
            animationDuration: 2500,
            animationEasing: 'cubicOut',
            itemStyle: {
              opacity: 0.9,
            },
          },
        ],
      });
    }, 300);

    // 第二阶段：城市标注点逐个出现
    setTimeout(() => {
      chartInstance.setOption({
        series: [
          {}, // 地图主体保持不变
          {
            // 城市标注点
            animationDuration: 1000,
            animationEasing: 'elasticOut',
            animationDelay: function (idx) {
              return idx * 300; // 每个点间隔300ms出现
            },
          },
        ],
      });
    }, 1800);

    // 第三阶段：添加标注点呼吸动画
    setTimeout(() => {
      addPointPulseAnimation();
    }, 3500);
  }

  // 添加标注点呼吸动画
  function addPointPulseAnimation() {
    const chartInstance = getInstance();
    if (!chartInstance) return;

    // 为在线状态的城市添加呼吸动画
    const onlineCities = cityData.filter((city) => city.status === 'online');

    setInterval(() => {
      chartInstance.setOption({
        series: [
          {}, // 地图主体
          {
            // 城市标注点
            data: cityData.map((city) => ({
              name: city.name,
              value: [...city.coord, 18],
              itemStyle: {
                color: function () {
                  if (city.status === 'online') {
                    return new echarts.graphic.RadialGradient(0.5, 0.5, 0.8, [
                      { offset: 0, color: '#FFFFFF' },
                      { offset: 0.3, color: '#40E0FF' },
                      { offset: 0.7, color: '#2E86C1' },
                      { offset: 1, color: '#1B4F72' },
                    ]);
                  } else {
                    return new echarts.graphic.RadialGradient(0.5, 0.5, 0.8, [
                      { offset: 0, color: '#FFFFFF' },
                      { offset: 0.3, color: '#E67E22' },
                      { offset: 0.7, color: '#D35400' },
                      { offset: 1, color: '#A04000' },
                    ]);
                  }
                },
                opacity: city.status === 'online' ? 1 : 0.95,
              },
              status: city.status,
              info: city.info,
            })),
            animationDuration: 2000,
            animationEasing: 'sinusoidalInOut',
          },
        ],
      });
    }, 4000); // 每4秒执行一次呼吸动画
  }

  // 处理地图鼠标悬停事件
  function handleMapMouseOver(params) {
    if (params.seriesType === 'scatter' || params.seriesType === 'scatter3D') {
      const cityInfo = cityData.find((city) => city.name === params.name);
      if (cityInfo) {
        showCityTooltip(params.event.offsetX, params.event.offsetY, cityInfo);
      }
    }
  }

  // 处理地图鼠标离开事件
  function handleMapMouseOut() {
    hideCityTooltip();
  }

  // 显示城市信息提示框
  function showCityTooltip(x, y, cityInfo) {
    let tooltip = document.querySelector('.city-tooltip');
    if (!tooltip) {
      tooltip = document.createElement('div');
      tooltip.className = 'city-tooltip';
      document.body.appendChild(tooltip);
    }

    tooltip.innerHTML = `
      <div style="font-weight: bold; color: ${cityInfo.status === 'online' ? '#40e0ff' : '#ff6b35'}; margin-bottom: 8px;">
        ${cityInfo.name}
      </div>
      <div style="line-height: 1.5;">
        ${cityInfo.info}
      </div>
    `;

    tooltip.style.left = x + 15 + 'px';
    tooltip.style.top = y - 10 + 'px';
    tooltip.style.display = 'block';
  }

  // 隐藏提示框
  function hideCityTooltip() {
    const tooltip = document.querySelector('.city-tooltip');
    if (tooltip) {
      tooltip.style.display = 'none';
    }
  }

  // 加载中国地图数据
  async function loadChinaMapData() {
    try {
      // 直接从 public 目录加载地图数据
      const response = await fetch('/json/100000_full.json');

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const geoJson = await response.json();

      // 验证地图数据格式
      if (!geoJson || !geoJson.features) {
        throw new Error('Invalid map data format');
      }

      // 注册地图数据到 ECharts
      echarts.registerMap('china', geoJson);

      // 等待一帧后初始化地图，确保注册完成
      requestAnimationFrame(() => {
        initMap();
      });
    } catch (error) {
      // 如果加载失败，显示错误信息
      console.error('地图数据加载失败:', error);
    }
  }

  // 处理地图点击事件
  function handleMapClick(params) {
    if (params.seriesType === 'scatter' || params.seriesType === 'scatter3D') {
      const cityInfo = cityData.find((city) => city.name === params.name);
      if (cityInfo) {
        // 这里可以添加弹窗显示详细信息的逻辑
        // 例如：showLocationDetail(cityInfo);
      }
    }
  }

  onMounted(() => {
    setTimeout(() => {
      loadChinaMapData();
    }, 100);
  });
</script>

<style scoped>
  /* 动画关键帧定义 */
  @keyframes tooltip-fade-in {
    from {
      transform: translateY(-10px) scale(0.9);
      opacity: 0;
    }

    to {
      transform: translateY(-5px) scale(1);
      opacity: 1;
    }
  }

  @keyframes map-fade-in {
    from {
      transform: scale(0.95);
      opacity: 0;
    }

    to {
      transform: scale(1);
      opacity: 1;
    }
  }

  @keyframes point-pulse {
    0%,
    100% {
      transform: scale(1);
      opacity: 0.9;
    }

    50% {
      transform: scale(1.1);
      opacity: 1;
    }
  }

  @keyframes title-shimmer {
    0%,
    100% {
      background-position: 0% 50%;
    }

    50% {
      background-position: 100% 50%;
    }
  }

  /* 响应式设计 */
  @media (max-width: 1400px) {
    .metrics-grid {
      grid-template-columns: repeat(2, 1fr);
      grid-template-rows: repeat(2, 1fr);
      gap: 12px;
    }

    .metrics-container {
      height: 180px;
    }

    .metric-content {
      padding: 15px;
    }

    .metric-value {
      font-size: 28px;
    }
  }

  @media (max-width: 1200px) {
    .map-chart {
      min-height: 250px;
    }

    .metrics-grid {
      grid-template-columns: repeat(4, 1fr);
      grid-template-rows: 1fr;
    }

    .metrics-container {
      height: 120px;
    }

    .metric-content {
      padding: 12px;
    }

    .metric-title {
      font-size: 12px;
    }

    .metric-value {
      font-size: 24px;
    }
  }

  @media (max-width: 768px) {
    .metrics-grid {
      grid-template-columns: repeat(2, 1fr);
      grid-template-rows: repeat(2, 1fr);
    }

    .metrics-container {
      height: 160px;
    }
  }

  .center-panel {
    display: flex;
    flex-direction: column;
    gap: 20px;
    height: 100%;
  }

  .map-container {
    position: relative;
    flex: 1;
    overflow: hidden;
    transition: all 0.3s ease;

    /* 恢复景深效果 - 使用更微妙的样式 */
    border: 1px solid rgba(64, 224, 255, 15%);
    border-radius: 12px;
    background: linear-gradient(135deg, rgba(11, 36, 59, 10%) 0%, rgba(46, 134, 193, 5%) 100%);
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 20%),
      inset 0 1px 0 rgba(255, 255, 255, 5%);
    backdrop-filter: blur(8px);
  }

  .map-container:hover {
    transform: translateY(-2px);
    border-color: rgba(64, 224, 255, 40%);
    box-shadow:
      0 12px 40px rgba(0, 0, 0, 30%),
      0 4px 20px rgba(64, 224, 255, 20%),
      inset 0 1px 0 rgba(255, 255, 255, 10%);
  }

  .map-header {
    padding: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 10%);
  }

  .map-title {
    margin: 0;
    animation: title-shimmer 3s ease-in-out infinite;
    background: linear-gradient(90deg, #40e0ff 0%, #fff 50%, #40e0ff 100%);
    background-clip: text;
    background-size: 200% 100%;
    color: #fff;
    font-size: 18px;
    font-weight: 600;
    text-align: center;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 50%);
    -webkit-text-fill-color: transparent;
  }

  .map-chart {
    position: relative;
    height: 100%;
    min-height: 300px;
    overflow: hidden;
    animation: map-fade-in 2s ease-out;

    /* 增强景深效果 */
    border-radius: 8px;
  }

  /* 为地图添加内阴影景深效果 */
  .map-chart::before {
    content: '';
    position: absolute;
    z-index: 1;
    inset: 0;
    border-radius: 8px;
    box-shadow:
      inset 0 0 30px rgba(0, 0, 0, 30%),
      inset 0 0 60px rgba(11, 36, 59, 20%);
    pointer-events: none;
  }

  .metrics-container {
    flex-shrink: 0;
    height: 140px;
  }

  .metrics-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 15px;
    height: 100%;
  }

  .metric-card {
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 10%);
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 30%);
    cursor: pointer;
    backdrop-filter: blur(10px);
  }

  .metric-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 40%);
  }

  .metric-content {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;
    padding: 20px;
  }

  .metric-header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    margin-bottom: 10px;
  }

  .metric-title {
    color: rgba(255, 255, 255, 90%);
    font-size: 14px;
    font-weight: 500;
    line-height: 1.2;
  }

  .metric-icon {
    color: rgba(255, 255, 255, 80%);
  }

  .metric-value {
    margin: 8px 0;
    color: #fff;
    font-size: 32px;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 50%);
  }

  .metric-change {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    font-weight: 500;
  }

  /* 城市提示框样式 - 优化后的高级玻璃态设计 */
  :global(.city-tooltip) {
    display: none;
    position: absolute;
    z-index: 1000;
    padding: 16px 20px;
    transform: translateY(-5px);
    animation: tooltip-fade-in 0.3s ease-out;
    border: 1.5px solid #40e0ff;
    border-radius: 12px;
    background: linear-gradient(135deg, rgba(11, 36, 59, 95%) 0%, rgba(46, 134, 193, 85%) 100%);
    box-shadow:
      0 8px 32px rgba(64, 224, 255, 40%),
      0 4px 16px rgba(0, 0, 0, 30%),
      inset 0 1px 0 rgba(255, 255, 255, 10%);
    color: #fff;
    font-size: 14px;
    font-weight: 500;
    line-height: 1.6;
    pointer-events: none;
    backdrop-filter: blur(15px);
  }
</style>
